import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/offline/sync_status.dart';
import 'package:culture_connect/services/translation_sync_service.dart';
import 'package:culture_connect/theme/app_colors.dart';

/// A widget that displays an indicator for offline translations
class OfflineTranslationIndicator extends ConsumerWidget {
  /// The message ID
  final String messageId;

  /// The sync status
  final SyncStatus syncStatus;

  /// Whether to show the indicator
  final bool showIndicator;

  /// The size of the indicator
  final double size;

  /// The color of the indicator
  final Color? color;

  /// Creates a new offline translation indicator
  const OfflineTranslationIndicator({
    super.key,
    required this.messageId,
    required this.syncStatus,
    this.showIndicator = true,
    this.size = 16.0,
    this.color,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    if (!showIndicator) {
      return const SizedBox.shrink();
    }

    // Get the sync status stream
    final syncStatusStream = ref.watch(translationSyncStatusProvider);

    return syncStatusStream.when(
      data: (status) {
        // If the sync service is syncing, show a syncing indicator
        if (status == SyncStatus.syncing) {
          return _buildSyncingIndicator();
        }

        // Otherwise, show the indicator based on the message's sync status
        return _buildStatusIndicator(syncStatus);
      },
      loading: () => _buildStatusIndicator(syncStatus),
      error: (_, __) => _buildStatusIndicator(syncStatus),
    );
  }

  /// Builds the indicator for a syncing status
  Widget _buildSyncingIndicator() {
    return SizedBox(
      width: size,
      height: size,
      child: CircularProgressIndicator(
        strokeWidth: 2.0,
        valueColor: AlwaysStoppedAnimation<Color>(
          color ?? AppColors.primary,
        ),
      ),
    );
  }

  /// Builds the indicator for a specific sync status
  Widget _buildStatusIndicator(SyncStatus status) {
    IconData iconData;
    Color iconColor;
    String tooltip;

    switch (status) {
      case SyncStatus.notSynced:
        iconData = Icons.cloud_off;
        iconColor = color ?? Colors.grey;
        tooltip = 'Not synced';
        break;
      case SyncStatus.pending:
        iconData = Icons.cloud_queue;
        iconColor = color ?? Colors.orange;
        tooltip = 'Pending sync';
        break;
      case SyncStatus.syncing:
        iconData = Icons.sync;
        iconColor = color ?? Colors.blue;
        tooltip = 'Syncing';
        break;
      case SyncStatus.synced:
        iconData = Icons.cloud_done;
        iconColor = color ?? Colors.green;
        tooltip = 'Synced';
        break;
      case SyncStatus.failed:
        iconData = Icons.cloud_off;
        iconColor = color ?? Colors.red;
        tooltip = 'Sync failed';
        break;
    }

    return Tooltip(
      message: tooltip,
      child: Icon(
        iconData,
        size: size,
        color: iconColor,
      ),
    );
  }
}

/// A widget that displays a badge for offline translations
class OfflineTranslationBadge extends StatelessWidget {
  /// The text to display
  final String text;

  /// The sync status
  final SyncStatus syncStatus;

  /// Whether to show the badge
  final bool showBadge;

  /// Creates a new offline translation badge
  const OfflineTranslationBadge({
    super.key,
    required this.text,
    required this.syncStatus,
    this.showBadge = true,
  });

  @override
  Widget build(BuildContext context) {
    if (!showBadge) {
      return const SizedBox.shrink();
    }

    Color badgeColor;
    String badgeText;

    switch (syncStatus) {
      case SyncStatus.notSynced:
        badgeColor = Colors.grey;
        badgeText = 'Offline';
        break;
      case SyncStatus.pending:
        badgeColor = Colors.orange;
        badgeText = 'Pending';
        break;
      case SyncStatus.syncing:
        badgeColor = Colors.blue;
        badgeText = 'Syncing';
        break;
      case SyncStatus.synced:
        return const SizedBox.shrink(); // No badge for synced translations
      case SyncStatus.failed:
        badgeColor = Colors.red;
        badgeText = 'Failed';
        break;
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: badgeColor.withAlpha(51),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: badgeColor, width: 1),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            _getIconForStatus(syncStatus),
            size: 12,
            color: badgeColor,
          ),
          SizedBox(width: 4),
          Text(
            badgeText,
            style: TextStyle(
              color: badgeColor,
              fontSize: 10,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  /// Gets the icon for a specific sync status
  IconData _getIconForStatus(SyncStatus status) {
    switch (status) {
      case SyncStatus.notSynced:
        return Icons.cloud_off;
      case SyncStatus.pending:
        return Icons.cloud_queue;
      case SyncStatus.syncing:
        return Icons.sync;
      case SyncStatus.synced:
        return Icons.cloud_done;
      case SyncStatus.failed:
        return Icons.cloud_off;
    }
  }
}

/// A provider for the sync status of a message
final messageSyncStatusProvider =
    Provider.family<SyncStatus, String>((ref, messageId) {
  // In a real implementation, this would get the sync status from the database
  // For now, we'll just return a mock status
  return SyncStatus.pending;
});
