// Flutter imports
import 'package:flutter/material.dart';

// Package imports
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project imports
import 'package:culture_connect/models/translation/accent_model.dart';
import 'package:culture_connect/models/translation/dialect_model.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// A widget that displays dialect and accent information
class DialectAccentIndicator extends ConsumerWidget {
  /// The dialect to display
  final DialectModel? dialect;

  /// The accent to display
  final AccentModel? accent;

  /// The confidence score for the dialect detection (0.0 to 1.0)
  final double dialectConfidence;

  /// The confidence score for the accent detection (0.0 to 1.0)
  final double accentConfidence;

  /// The size of the indicator
  final double size;

  /// Whether to show the label
  final bool showLabel;

  /// Whether to show the confidence score
  final bool showConfidence;

  /// Whether to show the flag
  final bool showFlag;

  /// Callback when the indicator is tapped
  final VoidCallback? onTap;

  /// Creates a new dialect accent indicator
  const DialectAccentIndicator({
    super.key,
    this.dialect,
    this.accent,
    this.dialectConfidence = 1.0,
    this.accentConfidence = 1.0,
    this.size = 24.0,
    this.showLabel = true,
    this.showConfidence = true,
    this.showFlag = true,
    this.onTap,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // If no dialect or accent, show nothing
    if (dialect == null && accent == null) {
      return const SizedBox.shrink();
    }

    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(size / 2),
      child: Padding(
        padding: EdgeInsets.all(4),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Icon
            _buildIcon(),

            // Label
            if (showLabel) ...[
              const SizedBox(width: 4),
              _buildLabel(),
            ],
          ],
        ),
      ),
    );
  }

  /// Build the icon
  Widget _buildIcon() {
    return Stack(
      children: [
        // Base icon
        Container(
          width: size,
          height: size,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: _getIconColor().withAlpha(26), // 0.1 * 255 = 26
            border: Border.all(
              color: _getIconColor().withAlpha(128), // 0.5 * 255 = 128
              width: 1.5,
            ),
          ),
          child: Center(
            child: Icon(
              _getIconData(),
              size: size * 0.6,
              color: _getIconColor(),
            ),
          ),
        ),

        // Flag indicator (if showing flag and dialect is available)
        if (showFlag && dialect != null)
          Positioned(
            right: 0,
            bottom: 0,
            child: Container(
              width: size * 0.5,
              height: size * 0.5,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.white,
                border: Border.all(
                  color: Colors.grey.withAlpha(77), // 0.3 * 255 = 77
                  width: 1,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(26), // 0.1 * 255 = 26
                    blurRadius: 2,
                    spreadRadius: 0,
                    offset: const Offset(0, 1),
                  ),
                ],
              ),
              child: Center(
                child: Text(
                  dialect!.flag,
                  style: TextStyle(
                    fontSize: size * 0.3,
                  ),
                ),
              ),
            ),
          ),

        // Confidence indicator (if showing confidence)
        if (showConfidence)
          Positioned(
            right: showFlag ? size * 0.5 : 0,
            top: 0,
            child: _buildConfidenceIndicator(),
          ),
      ],
    );
  }

  /// Build the confidence indicator
  Widget _buildConfidenceIndicator() {
    // Use the lower of the two confidence scores
    final confidence = accent != null
        ? (dialectConfidence + accentConfidence) / 2
        : dialectConfidence;

    // Determine the color based on confidence
    Color color;
    if (confidence >= 0.8) {
      color = Colors.green;
    } else if (confidence >= 0.6) {
      color = Colors.orange;
    } else {
      color = Colors.red;
    }

    return Container(
      width: size * 0.3,
      height: size * 0.3,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: color,
        border: Border.all(
          color: Colors.white,
          width: 1,
        ),
      ),
    );
  }

  /// Build the label
  Widget _buildLabel() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        // Dialect name
        if (dialect != null)
          Text(
            dialect!.name,
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: AppTheme.textPrimaryColor,
            ),
          ),

        // Accent name
        if (accent != null)
          Text(
            accent!.name,
            style: const TextStyle(
              fontSize: 10,
              color: AppTheme.textSecondaryColor,
            ),
          ),

        // Confidence text (if showing confidence)
        if (showConfidence)
          Text(
            _getConfidenceText(),
            style: TextStyle(
              fontSize: 9,
              color: _getConfidenceColor(),
            ),
          ),
      ],
    );
  }

  /// Get the icon data
  IconData _getIconData() {
    if (accent != null) {
      return Icons.record_voice_over;
    } else if (dialect != null) {
      return Icons.language;
    } else {
      return Icons.help_outline;
    }
  }

  /// Get the icon color
  Color _getIconColor() {
    if (accent != null) {
      return Colors.purple;
    } else if (dialect != null) {
      return Colors.blue;
    } else {
      return Colors.grey;
    }
  }

  /// Get the confidence text
  String _getConfidenceText() {
    // Use the lower of the two confidence scores
    final confidence = accent != null
        ? (dialectConfidence + accentConfidence) / 2
        : dialectConfidence;

    if (confidence >= 0.8) {
      return 'High confidence';
    } else if (confidence >= 0.6) {
      return 'Medium confidence';
    } else {
      return 'Low confidence';
    }
  }

  /// Get the confidence color
  Color _getConfidenceColor() {
    // Use the lower of the two confidence scores
    final confidence = accent != null
        ? (dialectConfidence + accentConfidence) / 2
        : dialectConfidence;

    if (confidence >= 0.8) {
      return Colors.green;
    } else if (confidence >= 0.6) {
      return Colors.orange;
    } else {
      return Colors.red;
    }
  }
}
