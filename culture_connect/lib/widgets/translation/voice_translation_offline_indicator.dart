// Flutter imports
import 'package:flutter/material.dart';

// Package imports
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Confidence level for a translation
enum TranslationConfidenceLevel {
  /// High confidence
  high,

  /// Medium confidence
  medium,

  /// Low confidence
  low,
}

/// A widget that indicates whether a voice translation was performed offline
class VoiceTranslationOfflineIndicator extends ConsumerWidget {
  /// Whether the translation was performed offline
  final bool isOffline;

  /// The confidence level of the translation (0.0 to 1.0)
  final double confidence;

  /// The size of the indicator
  final double size;

  /// Whether to show the confidence level
  final bool showConfidence;

  /// Whether to show the label
  final bool showLabel;

  /// The color of the indicator
  final Color? color;

  /// Creates a new voice translation offline indicator
  const VoiceTranslationOfflineIndicator({
    super.key,
    required this.isOffline,
    this.confidence = 1.0,
    this.size = 16.0,
    this.showConfidence = true,
    this.showLabel = true,
    this.color,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final confidenceLevel = _getConfidenceLevel(confidence);
    final indicatorColor = color ?? _getColorForConfidence(confidenceLevel);

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Offline icon
        Icon(
          isOffline ? Icons.offline_bolt : Icons.cloud_done,
          size: size,
          color: indicatorColor,
        ),

        if (showLabel) ...[
          const SizedBox(width: 4),

          // Label
          Text(
            isOffline ? 'Offline' : 'Online',
            style: TextStyle(
              fontSize: size * 0.75,
              fontWeight: FontWeight.w500,
              color: indicatorColor,
            ),
          ),
        ],

        if (showConfidence) ...[
          const SizedBox(width: 4),

          // Confidence indicator
          _buildConfidenceIndicator(confidenceLevel, indicatorColor),
        ],
      ],
    );
  }

  /// Build the confidence indicator
  Widget _buildConfidenceIndicator(
    TranslationConfidenceLevel confidenceLevel,
    Color color,
  ) {
    // Number of dots to show based on confidence level
    final dotCount = confidenceLevel == TranslationConfidenceLevel.high
        ? 3
        : confidenceLevel == TranslationConfidenceLevel.medium
            ? 2
            : 1;

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(3, (index) {
        final isActive = index < dotCount;

        return Container(
          width: (size * 0.3),
          height: (size * 0.3),
          margin: EdgeInsets.symmetric(horizontal: 1),
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: isActive ? color : color.withAlpha(77), // 0.3 * 255 = 77
          ),
        );
      }),
    );
  }

  /// Get the confidence level based on the confidence value
  TranslationConfidenceLevel _getConfidenceLevel(double confidence) {
    if (confidence >= 0.8) {
      return TranslationConfidenceLevel.high;
    } else if (confidence >= 0.5) {
      return TranslationConfidenceLevel.medium;
    } else {
      return TranslationConfidenceLevel.low;
    }
  }

  /// Get the color for a confidence level
  Color _getColorForConfidence(TranslationConfidenceLevel level) {
    return switch (level) {
      TranslationConfidenceLevel.high => Colors.green,
      TranslationConfidenceLevel.medium => Colors.orange,
      TranslationConfidenceLevel.low => Colors.red,
    };
  }
}

/// A widget that shows a tooltip with information about offline voice translation
class VoiceTranslationTooltip extends StatelessWidget {
  /// The child widget
  final Widget child;

  /// Whether the translation was performed offline
  final bool isOffline;

  /// The confidence level of the translation (0.0 to 1.0)
  final double confidence;

  /// Creates a new voice translation tooltip
  const VoiceTranslationTooltip({
    super.key,
    required this.child,
    required this.isOffline,
    required this.confidence,
  });

  @override
  Widget build(BuildContext context) {
    final confidenceLevel = _getConfidenceLevel(confidence);
    final confidenceText = _getConfidenceText(confidenceLevel);

    final tooltipText = isOffline
        ? 'This voice translation was performed offline.\n'
            'Confidence: $confidenceText'
        : 'This voice translation was performed online.';

    return Tooltip(
      message: tooltipText,
      textStyle: const TextStyle(
        fontSize: 12,
        color: Colors.white,
      ),
      decoration: BoxDecoration(
        color: Colors.black.withAlpha(204), // 0.8 * 255 = 204
        borderRadius: BorderRadius.circular(4),
      ),
      child: child,
    );
  }

  /// Get the confidence level based on the confidence value
  TranslationConfidenceLevel _getConfidenceLevel(double confidence) {
    if (confidence >= 0.8) {
      return TranslationConfidenceLevel.high;
    } else if (confidence >= 0.5) {
      return TranslationConfidenceLevel.medium;
    } else {
      return TranslationConfidenceLevel.low;
    }
  }

  /// Get the text description for a confidence level
  String _getConfidenceText(TranslationConfidenceLevel level) {
    return switch (level) {
      TranslationConfidenceLevel.high => 'High',
      TranslationConfidenceLevel.medium => 'Medium',
      TranslationConfidenceLevel.low => 'Low',
    };
  }
}

/// A widget that shows a badge indicating offline voice translation
class VoiceTranslationOfflineBadge extends StatelessWidget {
  /// Whether the translation was performed offline
  final bool isOffline;

  /// The confidence level of the translation (0.0 to 1.0)
  final double confidence;

  /// The size of the badge
  final double size;

  /// Creates a new voice translation offline badge
  const VoiceTranslationOfflineBadge({
    super.key,
    required this.isOffline,
    required this.confidence,
    this.size = 16.0,
  });

  @override
  Widget build(BuildContext context) {
    if (!isOffline) {
      return const SizedBox.shrink();
    }

    final confidenceLevel = _getConfidenceLevel(confidence);
    final backgroundColor = _getColorForConfidence(confidenceLevel);

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.offline_bolt,
            size: size,
            color: Colors.white,
          ),
          const SizedBox(width: 4),
          Text(
            'Offline',
            style: TextStyle(
              fontSize: size * 0.75,
              fontWeight: FontWeight.w500,
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  /// Get the confidence level based on the confidence value
  TranslationConfidenceLevel _getConfidenceLevel(double confidence) {
    if (confidence >= 0.8) {
      return TranslationConfidenceLevel.high;
    } else if (confidence >= 0.5) {
      return TranslationConfidenceLevel.medium;
    } else {
      return TranslationConfidenceLevel.low;
    }
  }

  /// Get the color for a confidence level
  Color _getColorForConfidence(TranslationConfidenceLevel level) {
    return switch (level) {
      TranslationConfidenceLevel.high => Colors.green,
      TranslationConfidenceLevel.medium => Colors.orange,
      TranslationConfidenceLevel.low => Colors.red,
    };
  }
}
