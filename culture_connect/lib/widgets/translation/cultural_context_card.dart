import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:culture_connect/models/translation/cultural_context_model.dart';
import 'package:culture_connect/models/translation/translation_cultural_context.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/translation/cultural_context_dialog.dart';

import 'package:culture_connect/utils/rtl_utils.dart';

/// A card that displays cultural context information
class CulturalContextCard extends StatelessWidget {
  /// The cultural context to display
  final TranslationCulturalContext culturalContext;

  /// Whether to show the full content
  final bool showFullContent;

  /// Whether to show the header
  final bool showHeader;

  /// Whether to animate the card
  final bool animate;

  /// Callback when the card is tapped
  final VoidCallback? onTap;

  /// Creates a new cultural context card
  const CulturalContextCard({
    super.key,
    required this.culturalContext,
    this.showFullContent = false,
    this.showHeader = true,
    this.animate = true,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    if (!culturalContext.hasContextInformation) {
      return const SizedBox.shrink();
    }

    // Determine text direction for target language
    final isRTL = RTLUtils.isRTL(culturalContext.targetLanguage);
    final textDirection =
        RTLUtils.getTextDirection(culturalContext.targetLanguage);

    final card = Directionality(
      textDirection: textDirection,
      child: Card(
        margin: EdgeInsets.symmetric(vertical: 8),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        elevation: 2,
        child: InkWell(
          onTap: onTap ??
              () {
                showDialog(
                  context: context,
                  builder: (context) => CulturalContextDialog(
                    culturalContext: culturalContext,
                  ),
                );
              },
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment:
                  isRTL ? CrossAxisAlignment.end : CrossAxisAlignment.start,
              children: [
                // Header
                if (showHeader) _buildHeader(),

                // General cultural note
                if (culturalContext.generalCulturalNote != null) ...[
                  if (showHeader) SizedBox(height: 12),
                  _buildGeneralNote(),
                ],

                // Notes
                if (culturalContext.notes.isNotEmpty) ...[
                  SizedBox(height: 12),
                  _buildNotes(),
                ],
              ],
            ),
          ),
        ),
      ),
    );

    if (animate) {
      return card.animate().fadeIn(duration: 300.ms).slideY(
          begin: 0.1, end: 0, duration: 300.ms, curve: Curves.easeOutQuad);
    }

    return card;
  }

  /// Build the header
  Widget _buildHeader() {
    // Determine text direction for target language
    final isRTL = RTLUtils.isRTL(culturalContext.targetLanguage);

    return Row(
      children: [
        // Icon
        Icon(
          Icons.info_outline,
          size: 20,
          color: AppTheme.primaryColor,
        ),

        SizedBox(width: 8),

        // Title
        Text(
          'Cultural Context',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: AppTheme.textPrimaryColor,
          ),
        ),

        const Spacer(),

        // Language pair
        Container(
          padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: Colors.grey[200],
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                _getLanguageFlag(culturalContext.sourceLanguage),
                style: TextStyle(
                  fontSize: 12,
                ),
              ),
              Icon(
                isRTL ? Icons.arrow_back : Icons.arrow_forward,
                size: 12,
                color: Colors.grey,
              ),
              Text(
                _getLanguageFlag(culturalContext.targetLanguage),
                style: TextStyle(
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// Build the general note
  Widget _buildGeneralNote() {
    // Determine text direction for target language
    final isRTL = RTLUtils.isRTL(culturalContext.targetLanguage);
    final textDirection =
        RTLUtils.getTextDirection(culturalContext.targetLanguage);
    final textAlign = RTLUtils.getTextAlignment(culturalContext.targetLanguage);

    return Column(
      crossAxisAlignment:
          isRTL ? CrossAxisAlignment.end : CrossAxisAlignment.start,
      children: [
        Text(
          'General Information',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: Colors.grey[700],
          ),
          textAlign: textAlign,
        ),
        SizedBox(height: 4),
        Container(
          width: double.infinity,
          padding: EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: AppTheme.primaryColor.withAlpha(13),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: AppTheme.primaryColor.withAlpha(51),
              width: 1,
            ),
          ),
          child: Directionality(
            textDirection: textDirection,
            child: Text(
              culturalContext.generalCulturalNote!,
              style: TextStyle(
                fontSize: 14,
                color: Colors.black87,
              ),
              textAlign: textAlign,
            ),
          ),
        ),
      ],
    );
  }

  /// Build the notes
  Widget _buildNotes() {
    // Filter notes based on sensitivity
    final notes =
        culturalContext.notes.where((note) => !note.isSensitive).toList();

    if (notes.isEmpty) {
      return const SizedBox.shrink();
    }

    // Determine text direction for target language
    final isRTL = RTLUtils.isRTL(culturalContext.targetLanguage);
    final textAlign = RTLUtils.getTextAlignment(culturalContext.targetLanguage);

    return Column(
      crossAxisAlignment:
          isRTL ? CrossAxisAlignment.end : CrossAxisAlignment.start,
      children: [
        Text(
          'Cultural Notes',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: Colors.grey[700],
          ),
          textAlign: textAlign,
        ),

        SizedBox(height: 8),

        // Show all notes if showFullContent is true, otherwise show only the first 2
        ...notes
            .take(showFullContent ? notes.length : 2)
            .map((note) => _buildNoteItem(note)),

        // Show "View more" button if there are more notes
        if (!showFullContent && notes.length > 2) ...[
          SizedBox(height: 8),
          Center(
            child: TextButton.icon(
              onPressed: onTap,
              icon: Icon(
                Icons.visibility,
                size: 16,
                color: AppTheme.primaryColor,
              ),
              label: Text(
                'View ${notes.length - 2} more notes',
                style: TextStyle(
                  fontSize: 12,
                  color: AppTheme.primaryColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ],
      ],
    );
  }

  /// Build a note item
  Widget _buildNoteItem(CulturalContextNote note) {
    // Determine text direction for target language
    final isRTL = RTLUtils.isRTL(culturalContext.targetLanguage);
    final textDirection =
        RTLUtils.getTextDirection(culturalContext.targetLanguage);
    final textAlign = RTLUtils.getTextAlignment(culturalContext.targetLanguage);

    return Container(
      margin: EdgeInsets.only(bottom: 8),
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: note.type.color.withAlpha(13),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: note.type.color.withAlpha(51),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment:
            isRTL ? CrossAxisAlignment.end : CrossAxisAlignment.start,
        children: [
          // Type badge
          Row(
            mainAxisAlignment:
                isRTL ? MainAxisAlignment.end : MainAxisAlignment.start,
            children: [
              Container(
                padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: note.type.color.withAlpha(25),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      note.type.icon,
                      size: 12,
                      color: note.type.color,
                    ),
                    SizedBox(width: 4),
                    Text(
                      note.type.displayName,
                      style: TextStyle(
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                        color: note.type.color,
                      ),
                    ),
                  ],
                ),
              ),

              // Region badge (if available)
              if (note.region != null) ...[
                SizedBox(width: 8),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.grey[200],
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    note.region!,
                    style: TextStyle(
                      fontSize: 10,
                      color: Colors.grey[700],
                    ),
                  ),
                ),
              ],
            ],
          ),

          SizedBox(height: 8),

          // Text segment
          if (note.textSegment.isNotEmpty) ...[
            Directionality(
              textDirection: textDirection,
              child: Text(
                '"${note.textSegment}"',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  fontStyle: FontStyle.italic,
                  color: Colors.black87,
                ),
                textAlign: textAlign,
              ),
            ),
            SizedBox(height: 4),
          ],

          // Explanation
          Directionality(
            textDirection: textDirection,
            child: Text(
              note.explanation,
              style: TextStyle(
                fontSize: 12,
                color: Colors.black87,
              ),
              textAlign: textAlign,
            ),
          ),

          // Alternatives (if available)
          if (note.alternatives != null && note.alternatives!.isNotEmpty) ...[
            SizedBox(height: 8),
            Text(
              'Alternatives:',
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.bold,
                color: Colors.grey[700],
              ),
            ),
            SizedBox(height: 4),
            Wrap(
              spacing: 8,
              runSpacing: 4,
              children: note.alternatives!.map((alternative) {
                return Container(
                  padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(4),
                    border: Border.all(
                      color: Colors.grey[300]!,
                      width: 1,
                    ),
                  ),
                  child: Text(
                    alternative,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.black87,
                    ),
                  ),
                );
              }).toList(),
            ),
          ],
        ],
      ),
    );
  }

  /// Get the flag for a language code
  String _getLanguageFlag(String languageCode) {
    // This is a simplified version - in a real app, you would use a more comprehensive mapping
    switch (languageCode) {
      case 'en':
        return '🇺🇸';
      case 'fr':
        return '🇫🇷';
      case 'es':
        return '🇪🇸';
      case 'de':
        return '🇩🇪';
      case 'it':
        return '🇮🇹';
      case 'pt':
        return '🇵🇹';
      case 'ru':
        return '🇷🇺';
      case 'zh':
        return '🇨🇳';
      case 'ja':
        return '🇯🇵';
      case 'ko':
        return '🇰🇷';
      case 'ar':
        return '🇸🇦';
      case 'hi':
        return '🇮🇳';
      case 'bn':
        return '🇧🇩';
      case 'sw':
        return '🇰🇪';
      case 'yo':
        return '🇳🇬';
      case 'ha':
        return '🇳🇬';
      case 'ig':
        return '🇳🇬';
      default:
        return '🌐';
    }
  }
}
